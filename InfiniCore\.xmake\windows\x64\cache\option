{
    cpu = {
        showmenu = true,
        __scriptdir = [[D:\Documents\Study\科研\code\InfiniCore]],
        __sourceinfo_default = { },
        __sourceinfo_showmenu = { },
        default = true,
        description = "Whether to compile implementations for CPU",
        __sourceinfo_description = {
            ["Whether to compile implementations for CPU"] = {
                line = 25,
                file = [[.\xmake.lua]]
            }
        }
    },
    ["nv-gpu"] = {
        showmenu = true,
        __scriptdir = [[D:\Documents\Study\科研\code\InfiniCore]],
        __sourceinfo_default = { },
        __sourceinfo_showmenu = { },
        default = false,
        description = "Whether to compile implementations for Nvidia GPU",
        __sourceinfo_description = {
            ["Whether to compile implementations for Nvidia GPU"] = {
                line = 47,
                file = [[.\xmake.lua]]
            }
        }
    },
    omp = {
        showmenu = true,
        __scriptdir = [[D:\Documents\Study\科研\code\InfiniCore]],
        __sourceinfo_default = { },
        __sourceinfo_showmenu = { },
        default = true,
        description = "Enable or disable OpenMP support for cpu kernel",
        __sourceinfo_description = {
            ["Enable or disable OpenMP support for cpu kernel"] = {
                line = 31,
                file = [[.\xmake.lua]]
            }
        }
    }
}