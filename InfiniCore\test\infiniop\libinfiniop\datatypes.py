class InfiniDtype:
    INVALID = 0
    BYTE = 1
    BOOL = 2
    I8 = 3
    I16 = 4
    I32 = 5
    I64 = 6
    U8 = 7
    U16 = 8
    U32 = 9
    U64 = 10
    F8 = 11
    F16 = 12
    F32 = 13
    F64 = 14
    C8 = 15
    C16 = 16
    C32 = 17
    C64 = 18
    BF16 = 19


InfiniDtypeNames = {
    InfiniDtype.INVALID: "INVALID",
    InfiniDtype.BYTE: "BYTE",
    InfiniDtype.BOOL: "BOOL",
    InfiniDtype.I8: "I8",
    InfiniDtype.I16: "I16",
    InfiniDtype.I32: "I32",
    InfiniDtype.I64: "I64",
    InfiniDtype.U8: "U8",
    InfiniDtype.U16: "U16",
    InfiniDtype.U32: "U32",
    InfiniDtype.U64: "U64",
    InfiniDtype.F8: "F8",
    InfiniDtype.F16: "F16",
    InfiniDtype.F32: "F32",
    InfiniDtype.F64: "F64",
    InfiniDtype.C8: "C8",
    InfiniDtype.C16: "C16",
    InfiniDtype.C32: "C32",
    InfiniDtype.C64: "C64",
    InfiniDtype.BF16: "BF16",
}
