{
    files = {
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\devices\cpu\common_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\devices\cpu\cpu_handle.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\add\cpu\add_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\causal_softmax\cpu\causal_softmax_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\clip\cpu\clip_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\conv\cpu\conv_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\gemm\cpu\gemm_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\mul\cpu\mul_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\random_sample\cpu\random_sample_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\rearrange\cpu\rearrange_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\relu\cpu\relu_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\rms_norm\cpu\rms_norm_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\rope\cpu\rope_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\sin\cpu\sin_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\sub\cpu\sub_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\ops\swiglu\cpu\swiglu_cpu.cc.obj]],
        [[build\.objs\infiniop-cpu\windows\x64\release\src\infiniop\reduce\cpu\reduce.cc.obj]],
        [[build\windows\x64\release\infini-utils.lib]]
    },
    values = {
        [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]],
        {
            "-nologo",
            "-machine:x64",
            "/opt:ref",
            "/opt:icf"
        }
    }
}