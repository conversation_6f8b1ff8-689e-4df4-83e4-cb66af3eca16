# InfiniCore算子开发深度调研报告

## 1. 项目概述

InfiniCore是一个跨平台统一编程工具集，为不同芯片平台提供统一的C语言接口。支持的硬件平台包括：
- CPU
- CUDA（英伟达、摩尔线程、天数智芯、沐曦、曙光DCU）
- 华为昇腾NPU
- 寒武纪MLU
- 昆仑芯XPU

## 2. 项目架构分析

### 2.1 模块体系
```
InfiniCore/
├── infini-utils/     # 全模块通用工具代码
├── infinirt/         # 运行时库，依赖infini-utils
├── infiniop/         # 算子库，依赖infinirt
├── infiniccl/        # 通信库，依赖infinirt
├── utils-test/       # 工具库测试代码
├── infiniop-test/    # 算子库测试框架
└── infiniccl-test/   # 通信库测试代码
```

### 2.2 文件目录结构
```
├── include/          # 对外暴露的头文件
│   ├── infiniop/     # 算子库头文件
│   └── *.h           # 模块核心头文件
├── src/              # 源代码目录
│   ├── infiniop/     # 算子库源代码
│   │   ├── devices/  # 各设备平台通用代码
│   │   ├── ops/      # 算子实现代码
│   │   ├── reduce/   # 规约类算子通用代码
│   │   └── elementwise/ # 逐元素类算子通用代码
│   ├── infinirt/     # 运行时库源代码
│   └── infiniccl/    # 通信库源代码
├── test/             # 测试代码
│   ├── infiniop/     # 算子单元测试
│   └── infiniop-test/ # 算子测试框架
└── scripts/          # 脚本目录
```

## 3. 算子实现框架

### 3.1 算子接口设计模式
每个算子都遵循统一的接口模式：
```c
// 创建算子描述符
infiniStatus_t infiniopCreate[Op]Descriptor(
    infiniopHandle_t handle,
    infiniop[Op]Descriptor_t *desc_ptr,
    infiniopTensorDescriptor_t output_desc,
    infiniopTensorDescriptor_t input_desc...
);

// 获取工作空间大小
infiniStatus_t infiniopGet[Op]WorkspaceSize(
    infiniop[Op]Descriptor_t desc, 
    size_t *size
);

// 执行算子计算
infiniStatus_t infiniop[Op](
    infiniop[Op]Descriptor_t desc,
    void *workspace,
    size_t workspace_size,
    void *output,
    const void *input...,
    void *stream
);

// 销毁算子描述符
infiniStatus_t infiniopDestroy[Op]Descriptor(
    infiniop[Op]Descriptor_t desc
);
```

### 3.2 多平台支持机制
算子实现采用分层架构：
1. **通用接口层**：`src/infiniop/ops/[op]/operator.cc`
2. **平台实现层**：`src/infiniop/ops/[op]/[device]/`
3. **通用框架层**：`elementwise/`、`reduce/`等

### 3.3 Elementwise算子框架
对于逐元素算子，框架提供了高度抽象的实现：
```cpp
// CPU实现示例
namespace op::add::cpu {
typedef struct AddOp {
    static constexpr size_t num_inputs = 2;
    template <typename T>
    T operator()(const T &a, const T &b) const {
        return a + b;
    }
} AddOp;
}

// CUDA实现示例
namespace op::add::cuda {
typedef struct AddOp {
    static constexpr size_t num_inputs = 2;
    template <typename T>
    __device__ __forceinline__ T operator()(const T &a, const T &b) const {
        return a + b;
    }
} AddOp;
}
```

## 4. 九齿（Ninetoothed）DSL支持

### 4.1 九齿简介
九齿是基于Triton的领域特定语言，提供更高层抽象，降低算子开发门槛。

### 4.2 九齿集成流程
1. 安装九齿和算子库：`pip install -e ntops`
2. AOT编译：`PYTHONPATH=src/ python scripts/build_ntops.py`
3. 编译时启用：`--ninetoothed=y`

## 5. 测试框架

### 5.1 Python单元测试
- 位置：`test/infiniop/[operator].py`
- 与PyTorch实现对比验证正确性和性能
- 支持多种数据类型和张量形状

### 5.2 GGUF测试框架
- 位置：`test/infiniop-test/`
- 使用GGUF文件格式存储测例
- 支持复杂的张量布局和步长测试

## 6. 构建系统

### 6.1 XMake配置
- 主配置文件：`xmake.lua`
- 平台特定配置：`xmake/[platform].lua`
- 支持多种编译选项和平台切换

### 6.2 一键安装脚本
```bash
python scripts/install.py [XMAKE_CONFIG_FLAGS]
```

## 7. 开发规范

### 7.1 代码命名规范
- 类型：`UpperCamelCase`
- 变量：`snake_case`
- 函数：`lowerCamelCase`
- 常量：`INFINI_UPPER_SNAKE_CASE`

### 7.2 代码格式化
- C/C++：`clang-format-16`
- Python：`black`
- 脚本：`python scripts/format.py`

## 8. 关键发现

1. **高度模块化**：清晰的分层架构，便于扩展新平台和算子
2. **统一接口**：所有算子遵循相同的API模式
3. **框架复用**：elementwise和reduce等通用框架大大简化开发
4. **多语言支持**：支持C++和九齿DSL两种实现方式
5. **完善测试**：双重测试体系确保算子质量
6. **跨平台**：统一代码库支持多种硬件平台

这个架构设计为AI算子开发提供了强大而灵活的基础设施，特别适合比赛中快速实现和优化算子。
