{
    files = {
        [[build\.objs\infinirt-nvidia\windows\x64\release\src\infinirt\cuda\infinirt_cuda.cu.obj]],
        [[build\.objs\infinirt-nvidia\windows\x64\release\rules\cuda\devlink\infinirt-nvidia_gpucode.cu.obj]],
        [[build\windows\x64\release\infini-utils.lib]]
    },
    values = {
        [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]],
        {
            "-nologo",
            "-machine:x64",
            "/opt:ref",
            "/opt:icf"
        }
    }
}