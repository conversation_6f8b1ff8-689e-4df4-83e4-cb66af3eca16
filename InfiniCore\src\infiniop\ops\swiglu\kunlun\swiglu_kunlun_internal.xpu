#ifndef __SWIGLU_KUNLUN_H__
#define __SWIGLU_KUNLUN_H__

#include "../../../devices/kunlun/kunlun_kernel_common.h"
#include "../../../elementwise/kunlun/elementwise_kunlun_kernel.h"

/// @brief Define swiglu op for local mem
typedef struct SwiGLUOp {
private:
    template <typename T>
    inline __device__ T sigmoid(T x) const {
        return 1.0f / (1.0f + exp(-x));
    }

public:
    // This static number must be set in other Ops
    static constexpr size_t num_inputs = 2;
    template <typename T>
    inline __device__ T operator()(const T *inputs) const {
        T up = inputs[0];
        T gate = inputs[1];
        T out = gate * sigmoid(gate) * up;
        return out;
    }
} SwiGLUOp;

// Definition for swiglu kernel interface
LAUNCH_ELEMENTWISE_KERNEL_IMPL(SwiGLU, SwiGLUOp)

// Template instantiate
LAUNCH_ELEMENTWISE_KERNEL_INSTANTIATE(SwiGLU, float)

#endif // __SWIGLU_KUNLUN_H__
