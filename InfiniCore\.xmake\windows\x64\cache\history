{
    cmdlines = {
        "xmake f --nv-gpu=y -cv",
        "xmake ",
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\modules\\\\private\\\\utils\\\\statistics.lua"]],
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\actions\\\\build\\\\cleaner.lua"]],
        "xmake f --cpu=y --nv-gpu=n -cv",
        "xmake ",
        "xmake install",
        "xmake build infiniop-test",
        "xmake install infiniop-test",
        "xmake f --nv-gpu=y -cv",
        "xmake ",
        "xmake f --nv-gpu=y -cv --verbose",
        "xmake f --nv-gpu=y -cv",
        "xmake ",
        "xmake f --cpu=y -cv",
        "xmake build",
        "xmake install",
        "xmake f --cpu=y --nv-gpu=y -cv",
        "xmake build",
        "xmake f --cpu=y --nv-gpu=y --cudnn=n -cv",
        "xmake build",
        "xmake build",
        "xmake f --cpu=y --nv-gpu=y --cudnn=n -cv",
        "xmake build",
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\modules\\\\private\\\\utils\\\\statistics.lua"]],
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\actions\\\\build\\\\cleaner.lua"]],
        "xmake ",
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\modules\\\\private\\\\utils\\\\statistics.lua"]],
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\actions\\\\build\\\\cleaner.lua"]],
        "xmake ",
        "xmake ",
        "xmake f --cpu=y --nv-gpu=y --cudnn=n -cv",
        "xmake build infiniop",
        "xmake build infiniop"
    }
}