{
    ["tool_target_infiniccl-test_windows_x64_ld"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    tool_target_infiniop_windows_x64_ld = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    nasm_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows"
    },
    tool_platform_windows_x64_cu = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "cuda_arch_x64_plat_windows",
            name = "cuda"
        },
        toolname = "nvcc",
        program = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    fpc_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows"
    },
    cuda_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows"
    },
    rust_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows"
    },
    tool_target_infiniop_windows_x64_sh = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    ["tool_target_infiniop-cpu_windows_x64_cxx"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    ["tool_target_infiniop-nvidia_windows_x64_cu"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "cuda_arch_x64_plat_windows",
            name = "cuda"
        },
        toolname = "nvcc",
        program = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    ["tool_target_infinirt-cpu_windows_x64_cxx"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    tool_target_infiniccl_windows_x64_ld = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    ["tool_target_infinirt-cpu_windows_x64_ar"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    ["tool_target_infiniutils-test_windows_x64_ld"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    ["tool_target_infiniop-test_windows_x64_ld"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    gfortran_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows"
    },
    ["tool_target_infini-utils_windows_x64_cxx"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    tool_target_infinirt_windows_x64_sh = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    tool_target_infiniop_windows_x64_cxx = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    yasm_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows"
    },
    ["tool_target_infinirt-nvidia_windows_x64_culd"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "cuda_arch_x64_plat_windows",
            name = "cuda"
        },
        toolname = "nvcc",
        program = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    ["tool_target_infinirt-nvidia_windows_x64_cu"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "cuda_arch_x64_plat_windows",
            name = "cuda"
        },
        toolname = "nvcc",
        program = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    tool_target_infinirt_windows_x64_cxx = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    swift_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows"
    },
    nim_arch_x64_plat_windows = {
        arch = "x64",
        __checked = false,
        __global = true,
        plat = "windows"
    },
    go_arch_x64_plat_windows = {
        arch = "x64",
        __checked = true,
        __global = true,
        plat = "windows"
    },
    ["tool_target_infiniop-nvidia_windows_x64_cxx"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    msvc_arch_x64_plat_windows = {
        vs_sdkver = "10.0.26100.0",
        __checked = "2022",
        vcarchs = {
            "arm",
            "arm64",
            "arm64ec",
            "x64",
            "x86"
        },
        plat = "windows",
        vs = "2022",
        arch = "x64",
        __global = true,
        vcvars = {
            UniversalCRTSdkDir = [[D:\Windows Kits\10\]],
            ExtensionSdkDir = [[C:\Program Files (x86)\Microsoft SDKs\Windows Kits\10\ExtensionSDKs]],
            LIB = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64;D:\Windows Kits\10\lib\10.0.26100.0\ucrt\x64;D:\Windows Kits\10\\lib\10.0.26100.0\\um\x64]],
            VCToolsInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\]],
            INCLUDE = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;D:\Windows Kits\10\include\10.0.26100.0\ucrt;D:\Windows Kits\10\\include\10.0.26100.0\\um;D:\Windows Kits\10\\include\10.0.26100.0\\shared;D:\Windows Kits\10\\include\10.0.26100.0\\winrt;D:\Windows Kits\10\\include\10.0.26100.0\\cppwinrt]],
            WindowsLibPath = [[D:\Windows Kits\10\UnionMetadata\10.0.26100.0;D:\Windows Kits\10\References\10.0.26100.0]],
            UCRTVersion = "10.0.26100.0",
            PATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\VCPackages;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TeamFoundation\Team Explorer;C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\bin\Roslyn;C:\Program Files\Microsoft Visual Studio\2022\Community\Team Tools\DiagnosticsHub\Collector;D:\Windows Kits\10\bin\10.0.26100.0\\x64;D:\Windows Kits\10\bin\\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;E:\Program Files (x86)\VMware\VMware Player\bin\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\dotnet\;C:\Program Files (x86)\STMicroelectronics\STM32 ST-LINK Utility\ST-LINK Utility;D:\Program Files\MATLAB\R2022b\runtime\win64;D:\Program Files\MATLAB\R2022b\bin;D:\Program Files\Git\cmd;D:\Documents\Study\myBlog\hugo_extended_0.134.3_windows-amd64;D:\Users\12914\Downloads\MinGW\bin;C:\Program Files (x86)\Prince\engine\bin;E:\CTags\ctags-2024-10-28_p6.1.20241027.0-4-gde111c5-x64;E:\Program Files\mingw6;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Zero Install;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\xmake;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.2.1\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;D:\Windows Kits\10\Windows Performance Toolkit\;D:\Users\12914\anaconda3;D:\Users\12914\anaconda3\Library\mingw-w64\bin;D:\Users\12914\anaconda3\Library\usr\bin;D:\Users\12914\anaconda3\Library\bin;D:\Users\12914\anaconda3\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Users\12914\AppData\Local\Programs\Microsoft VS Code\bin;E:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;;E:\Program Files\JetBrains\CLion 2024.2.2\bin;;E:\Program Files\JetBrains\PyCharm 2024.3.1.1\bin;;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\vcpkg]],
            VCToolsVersion = "14.44.35207",
            WindowsSdkDir = [[D:\Windows Kits\10\]],
            VS170COMNTOOLS = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\]],
            WindowsSdkBinPath = [[D:\Windows Kits\10\bin\]],
            VSInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\]],
            LIBPATH = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x86\store\references;D:\Windows Kits\10\UnionMetadata\10.0.26100.0;D:\Windows Kits\10\References\10.0.26100.0;C:\Windows\Microsoft.NET\Framework64\v4.0.30319]],
            VCToolsRedistDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Redist\MSVC\14.44.35112\]],
            WindowsSdkVerBinPath = [[D:\Windows Kits\10\bin\10.0.26100.0\]],
            VSCMD_ARG_HOST_ARCH = "x64",
            VCInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\]],
            VSCMD_VER = "17.14.9",
            VisualStudioVersion = "17.0",
            VSCMD_ARG_app_plat = "Desktop",
            WindowsSDKVersion = "10.0.26100.0",
            VCIDEInstallDir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\]],
            VSCMD_ARG_TGT_ARCH = "x64",
            DevEnvdir = [[C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\]]
        },
        vs_toolset = "14.44.35207"
    },
    tool_platform_windows_x64_cc = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    tool_platform_windows_x64_cxx = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "cl",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    ["tool_target_infini-utils_windows_x64_ar"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    ["tool_target_infinirt-nvidia_windows_x64_ar"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    tool_target_infinirt_windows_x64_ld = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    tool_platform_windows_x64_ld = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    tool_target_infiniccl_windows_x64_sh = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    ["tool_target_infiniop-cpu_windows_x64_ar"] = {
        toolchain_info = {
            arch = "x64",
            plat = "windows",
            cachekey = "msvc_arch_x64_plat_windows",
            name = "msvc"
        },
        toolname = "link",
        program = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    }
}