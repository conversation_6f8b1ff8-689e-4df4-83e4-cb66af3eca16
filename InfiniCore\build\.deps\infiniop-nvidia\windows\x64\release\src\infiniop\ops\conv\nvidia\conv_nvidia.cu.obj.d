{
    depfiles_format = "gcc",
    depfiles = "build/.objs/infiniop-nvidia/windows/x64/release/src/infiniop/ops/conv/nvidia/conv_nvidia.cu.obj : src/infiniop/ops/conv/nvidia/conv_nvidia.cu     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/ops/conv/nvidia/conv_nvidia.cu     C://Program\\ Files//NVIDIA\\ GPU\\ Computing\\ Toolkit//CUDA//v12.9//include/cuda_runtime.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/host_config.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/crtdefs.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdarg     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xkeycheck.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/builtin_types.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/device_types.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/host_defines.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/ctype.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wctype.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/driver_types.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/vector_types.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/stddef.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/surface_types.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/texture_types.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/library_types.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/channel_descriptor.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/cuda_runtime_api.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/cuda_device_runtime_api.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/stdlib.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_malloc.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_search.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wstdlib.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/driver_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/vector_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/vector_functions.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/common_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/host_defines.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/string.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_memory.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_memcpy_s.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/errno.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wstring.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/time.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wtime.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/exception     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/crtdbg.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_new_debug.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_new.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/use_ansi.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdlib     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/math.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_math.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xtr1common     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdint.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/malloc.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_exception.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/eh.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_terminate.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/stdio.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wstdio.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_stdio_config.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/assert.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/math_functions.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/math_functions.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/device_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/device_functions.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/device_atomic_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/device_atomic_functions.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/device_double_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/device_double_functions.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_20_atomic_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_20_atomic_functions.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_32_atomic_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_32_atomic_functions.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_35_atomic_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_60_atomic_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_60_atomic_functions.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_20_intrinsics.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_20_intrinsics.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_30_intrinsics.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_30_intrinsics.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_32_intrinsics.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_32_intrinsics.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_35_intrinsics.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_61_intrinsics.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/sm_61_intrinsics.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/sm_70_rt.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/sm_70_rt.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/sm_80_rt.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/sm_80_rt.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/sm_90_rt.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/sm_90_rt.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/sm_100_rt.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/sm_100_rt.hpp     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/texture_indirect_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/surface_indirect_functions.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/crt/cudacc_ext.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/device_launch_parameters.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/ops/conv/nvidia/../../../devices/nvidia/nvidia_common.cuh     include/infinicore.h     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/devices/nvidia/nvidia_handle.cuh     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/devices/nvidia/../../../utils.h     D:/Documents/Study/科研/code/InfiniCore/src/utils/custom_types.h     D:/Documents/Study/科研/code/InfiniCore/src/utils/rearrange.h     D:/Documents/Study/科研/code/InfiniCore/src/utils/result.hpp     D:/Documents/Study/科研/code/InfiniCore/src/utils/check.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iostream     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/istream     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_ostream.hpp     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ios     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xlocnum     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cfloat     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/float.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdio     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iosfwd     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cwchar     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/wchar.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wconio.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wdirect.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wio.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_share.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_wprocess.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/sys/stat.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/sys/types.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/intrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/intrin0.inl.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/setjmp.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/immintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/wmmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/nmmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/smmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tmmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/pmmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/emmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xmmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/mmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/zmmintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ammintrin.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xutility     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_iter_core.hpp     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/streambuf     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xiosbase     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/share.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/system_error     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_system_error_abi.hpp     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cerrno     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdexcept     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xstring     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_sanitizer_annotate_container.hpp     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_string_view.hpp     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xmemory     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/intrin0.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xatomic.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xpolymorphic_allocator.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xcall_once.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xerrc.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xthreads.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_threads_core.hpp     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xtimec.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ctime     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xlocale     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/typeinfo     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_typeinfo.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xfacet     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xlocinfo     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_xlocinfo_types.hpp     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cctype     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/clocale     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/locale.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ostream     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xsmf_control.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_bit_utils.hpp     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/devices/nvidia/../pool.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/mutex     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/__msvc_chrono.hpp     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/ratio     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/thread     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/process.h     D:/Windows\\ Kits/10/include/10.0.26100.0/ucrt/corecrt_startup.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_startup.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/devices/nvidia/nvidia_handle.h     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/devices/nvidia/../../handle.h     include/infiniop/handle.h     C://Program\\ Files//NVIDIA\\ GPU\\ Computing\\ Toolkit//CUDA//v12.9//include/cublas_v2.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/cublas_api.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/cuComplex.h     C://Program\\ Files//NVIDIA\\ GPU\\ Computing\\ Toolkit//CUDA//v12.9//include/cuda_fp16.h     C://Program\\ Files//NVIDIA\\ GPU\\ Computing\\ Toolkit//CUDA//v12.9//include/nv/target     C://Program\\ Files//NVIDIA\\ GPU\\ Computing\\ Toolkit//CUDA//v12.9//include/nv/detail/__target_macros     C://Program\\ Files//NVIDIA\\ GPU\\ Computing\\ Toolkit//CUDA//v12.9//include/nv/detail/__preprocessor     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/cuda_fp16.hpp     C://Program\\ Files//NVIDIA\\ GPU\\ Computing\\ Toolkit//CUDA//v12.9//include/cuda_bf16.h     C:/Program\\ Files/NVIDIA\\ GPU\\ Computing\\ Toolkit/CUDA/v12.9/include/cuda_bf16.hpp     C://Program\\ Files//NVIDIA\\ GPU\\ Computing\\ Toolkit//CUDA//v12.9//include/library_types.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xhash     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xbit_ops.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/xnode_handle.h     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/ops/conv/nvidia/conv_nvidia.cuh     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/ops/conv/nvidia/../conv.h     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/ops/conv/../../operator.h     include/infiniop/operator_descriptor.h     D:/Documents/Study/科研/code/InfiniCore/include/infiniop/tensor_descriptor.h     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/ops/conv/info.h     D:/Documents/Study/科研/code/InfiniCore/src/infiniop/ops/conv/../../tensor.h     C:/Program\\ Files/Microsoft\\ Visual\\ Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string\
",
    files = {
        [[src\infiniop\ops\conv\nvidia\conv_nvidia.cu]]
    },
    values = {
        [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]],
        {
            "-Xcompiler",
            "\"-MD\"",
            "-O3",
            "-Iinclude",
            [[-IC:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include]],
            "--std",
            "c++17",
            "-DENABLE_CPU_API",
            "-DENABLE_OMP",
            "-DENABLE_NVIDIA_API",
            "-Xcompiler=/utf-8",
            "--expt-relaxed-constexpr",
            "--allow-unsupported-compiler",
            "-Xcompiler=/W3",
            "-Xcompiler=/WX",
            "-m64",
            "-rdc=true",
            "-gencode",
            "arch=compute_89,code=sm_89",
            "-DNDEBUG"
        }
    }
}