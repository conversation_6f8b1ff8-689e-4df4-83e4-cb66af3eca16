{
    find_program_msvc_arch_x64_plat_windows_checktoolld = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    find_program_gfortran_arch_x64_plat_windows_checktoolfc = {
        gfortran = false,
        g95 = false
    },
    ["lib.detect.has_flags"] = {
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxxflags_-nologo_/openmp"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-MD -MF"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx__-nologo_-O2"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu___-O3"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-Xcompiler=/W3"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-rdc=true"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-Xcompiler=-Wno-error=deprecated-declarations"] = false,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__--expt-relaxed-constexpr"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxflags_-nologo_-DNDEBUG"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxflags_-nologo_/wd4068"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__--allow-unsupported-compiler"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxflags_-nologo_cl_sourceDependencies"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-Xcompiler=/utf-8"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-gencode arch=compute_89,code=sm_89"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxflags_-nologo_/openmp"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-DNDEBUG"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-Xcompiler=/WX"] = true
    },
    find_program_cuda_arch_x64_plat_windows_checktoolfc = {
        nvfortran = false
    },
    find_program = {
        gzip = false,
        nim = false,
        git = [[D:\Program Files\Git\cmd\git.exe]],
        nvcc = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]],
        ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        ["7z"] = [[C:\Program Files\xmake\winenv\bin\7z]],
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        ["C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc"] = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    find_program_cuda_arch_x64_plat_windows_checktoolcu = {
        nvcc = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    ["detect.sdks.find_cuda"] = {
        cuda = {
            sdkdir = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9]],
            includedirs = {
                [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include]]
            },
            linkdirs = {
                [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64]]
            },
            msbuildextensionsdir = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\extras\visual_studio_integration\MSBuildExtensions]],
            bindir = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin]],
            version = "12.9"
        }
    },
    find_program_msvc_arch_x64_plat_windows_checktoolar = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    find_program_msvc_arch_x64_plat_windows_checktoolcxx = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    find_program_cuda_arch_x64_plat_windows_checktoolculd = {
        nvcc = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    find_cudadevices = {
        succeed = true,
        data = {
            {
                ["$flops"] = 2903040000,
                minor = 9,
                warpSize = 32,
                isMultiGpuBoard = false,
                maxTexture1D = 131072,
                multiProcessorCount = 24,
                computeMode = 0,
                concurrentKernels = true,
                maxSurfaceCubemap = 32768,
                pciBusID = 1,
                ECCEnabled = false,
                tccDriver = false,
                pageableMemoryAccess = false,
                surfaceAlignment = 512,
                maxThreadsDim = {
                    1024,
                    1024,
                    64
                },
                uuid = "75d4107f-ac55-161a-f93a-d75e7f4a675c",
                integrated = false,
                pciDomainID = 0,
                totalConstMem = 65536,
                major = 8,
                maxSurfaceCubemapLayered = {
                    32768,
                    2046
                },
                name = "NVIDIA GeForce RTX 4060 Laptop GPU",
                maxSurface2D = {
                    131072,
                    65536
                },
                pageableMemoryAccessUsesHostPageTables = false,
                memoryClockRate = 8001000,
                multiGpuBoardGroupID = 0,
                l2CacheSize = 33554432,
                localL1CacheSupported = true,
                memPitch = 2147483647,
                maxTextureCubemap = 32768,
                maxSurface2DLayered = {
                    32768,
                    32768,
                    2048
                },
                ["$id"] = 0,
                maxTexture3D = {
                    16384,
                    16384,
                    16384
                },
                pciDeviceID = 0,
                regsPerBlock = 65536,
                maxTexture2DLayered = {
                    32768,
                    32768,
                    2048
                },
                sharedMemPerBlockOptin = 101376,
                unifiedAddressing = true,
                regsPerMultiprocessor = 65536,
                singleToDoublePrecisionPerfRatio = 64,
                clockRate = 1890000,
                canMapHostMemory = true,
                concurrentManagedAccess = false,
                cooperativeMultiDeviceLaunch = false,
                maxTexture1DLinear = 268435456,
                maxTexture3DAlt = {
                    8192,
                    8192,
                    32768
                },
                cooperativeLaunch = true,
                streamPrioritiesSupported = true,
                maxTexture2DGather = {
                    32768,
                    32768
                },
                managedMemory = true,
                directManagedMemAccessFromHost = false,
                maxTexture1DLayered = {
                    32768,
                    2048
                },
                luidDeviceNodeMask = 1,
                asyncEngineCount = 1,
                sharedMemPerMultiprocessor = 102400,
                maxSurface1DLayered = {
                    32768,
                    2048
                },
                texturePitchAlignment = 32,
                maxThreadsPerBlock = 1024,
                maxTexture2DMipmap = {
                    32768,
                    32768
                },
                maxTexture2D = {
                    131072,
                    65536
                },
                maxTexture1DMipmap = 32768,
                globalL1CacheSupported = true,
                maxTextureCubemapLayered = {
                    32768,
                    2046
                },
                memoryBusWidth = 128,
                maxGridSize = {
                    2147483647,
                    65535,
                    65535
                },
                maxTexture2DLinear = {
                    131072,
                    65000,
                    2097120
                },
                textureAlignment = 512,
                maxThreadsPerMultiProcessor = 1536,
                kernelExecTimeoutEnabled = true,
                deviceOverlap = true,
                maxSurface3D = {
                    16384,
                    16384,
                    16384
                },
                totalGlobalMem = 8585216000,
                canUseHostPointerForRegisteredMem = false,
                maxSurface1D = 32768,
                luid = "fdbd530f00000000",
                sharedMemPerBlock = 49152,
                computePreemptionSupported = true
            }
        }
    },
    find_program_cuda_arch_x64_plat_windows_checktoolcxx = {
        ["nvc++"] = false
    },
    ["core.tools.nvcc.has_flags"] = {
        ["C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9"] = {
            ["--extended-lambda"] = true,
            ["-lineinfo"] = true,
            ["--generate-dependencies"] = true,
            ["--Wreorder"] = true,
            ["--preprocess"] = true,
            ["--x"] = true,
            ["--archive-options"] = true,
            ["--gpu-architecture"] = true,
            ["--shared"] = true,
            ["--ftemplate-depth"] = true,
            ["--diag-warn"] = true,
            ["--debug"] = true,
            ["--options-file"] = true,
            ["--system-include"] = true,
            ["--machine"] = true,
            ["-o"] = true,
            ["--compiler-bindir"] = true,
            ["--no-align-double"] = true,
            ["-dw"] = true,
            ["--input-drive-prefix"] = true,
            ["--device-link"] = true,
            ["--optimization-info"] = true,
            ["--nvlink-options"] = true,
            ["--generate-line-info"] = true,
            ["--Wext-lambda-captures-this"] = true,
            ["--list-gpu-arch"] = true,
            ["--diag-suppress"] = true,
            ["--device-w"] = true,
            ["--generate-code"] = true,
            ["--Ofast-compile"] = true,
            ["--compile"] = true,
            ["--dependency-drive-prefix"] = true,
            ["-code"] = true,
            ["--no-host-device-initializer-list"] = true,
            ["-Turning"] = true,
            ["--ltoir"] = true,
            ["--ftemplate-backtrace-limit"] = true,
            ["--diag-error"] = true,
            ["--force-cl-env-setup"] = true,
            ["--relocatable-ptx"] = true,
            ["--define-macro"] = true,
            ["--keep-device-functions"] = true,
            ["--extra-device-vectorization"] = true,
            ["--extensible-whole-program"] = true,
            ["--brief-diagnostics"] = true,
            ["--help"] = true,
            ["--prec-sqrt"] = true,
            ["--Wdefault-stream-launch"] = true,
            ["--output-directory"] = true,
            ["--Wno-deprecated-gpu-targets"] = true,
            ["--keep"] = true,
            ["--ptxas-options"] = true,
            ["--dependency-output"] = true,
            ["--generate-dependency-targets"] = true,
            ["--list-gpu-code"] = true,
            ["-arch"] = true,
            ["-ccbin"] = true,
            ["--gpu-code"] = true,
            ["--compiler-options"] = true,
            ["--device-stack-protector"] = true,
            ["--compile-as-tools-patch"] = true,
            ["--run-args"] = true,
            ["-MF"] = true,
            ["--cudart"] = true,
            ["--display-error-number"] = true,
            ["-frandom-seed"] = true,
            ["--expt-extended-lambda"] = true,
            ["--lib"] = true,
            ["--dependency-target-name"] = true,
            ["-This"] = true,
            ["-dc"] = true,
            ["--generate-dependencies-with-compile"] = true,
            ["--split-compile-extended"] = true,
            ["--device-c"] = true,
            ["--output-file"] = true,
            ["--Werror"] = true,
            ["--pre-include"] = true,
            ["--link"] = true,
            ["--dopt"] = true,
            ["--linker-options"] = true,
            ["--entries"] = true,
            ["--target-directory"] = true,
            ["--std"] = true,
            ["--lto"] = true,
            ["--prec-div"] = true,
            ["--run"] = true,
            ["--m64"] = true,
            ["-dlink"] = true,
            ["--forward-slash-prefix-opts"] = true,
            ["-bar"] = true,
            ["--clean-targets"] = true,
            ["--fmad"] = true,
            ["--jump-table-density"] = true,
            ["--threads"] = true,
            ["-gencode"] = true,
            ["--save-temps"] = true,
            ["--fatbin"] = true,
            ["--objdir-as-tempdir"] = true,
            ["--relocatable-device-code"] = true,
            ["--no-compress"] = true,
            ["--version"] = true,
            ["--archiver-binary"] = true,
            ["--libdevice-directory"] = true,
            ["--maxrregcount"] = true,
            ["--library-path"] = true,
            ["--device-entity-has-hidden-visibility"] = true,
            ["--device-debug"] = true,
            ["--restrict"] = true,
            ["--undefine-macro"] = true,
            ["--library"] = true,
            ["--Wmissing-launch-bounds"] = true,
            ["--ptx"] = true,
            ["--fdevice-time-trace"] = true,
            ["--profile"] = true,
            ["-forward-slash-prefix-opts"] = true,
            ["--include-path"] = true,
            ["-forward-unknown-opts"] = true,
            ["-forward-unknown-to-host-linker"] = true,
            ["--forward-unknown-opts"] = true,
            ["--forward-unknown-to-host-linker"] = true,
            ["-forward-unknown-to-host-compiler"] = true,
            ["--time"] = true,
            ["-dlto"] = true,
            ["--fdevice-syntax-only"] = true,
            ["--forward-unknown-to-host-compiler"] = true,
            ["--no-host-device-move-forward"] = true,
            ["--cuda"] = true,
            ["--cudadevrt"] = true,
            ["--optimize"] = true,
            ["--allow-unsupported-compiler"] = true,
            ["--dryrun"] = true,
            ["--static-global-template-stub"] = true,
            ["--verbose"] = true,
            ["--ftz"] = true,
            ["--expt-relaxed-constexpr"] = true,
            ["--use-local-env"] = true,
            ["--compress-mode"] = true,
            ["--optix-ir"] = true,
            ["--dlink-time-opt"] = true,
            ["--no-device-link"] = true,
            ["--cubin"] = true,
            ["--source-in-ptx"] = true,
            ["--gen-opt-lto"] = true,
            ["--disable-warnings"] = true,
            ["--no-display-error-number"] = true,
            ["--frandom-seed"] = true,
            ["--keep-dir"] = true,
            ["--dont-use-profile"] = true,
            ["--resource-usage"] = true,
            ["--default-stream"] = true,
            ["--drive-prefix"] = true,
            ["--Wno-deprecated-declarations"] = true,
            ["--split-compile"] = true,
            ["--no-exceptions"] = true
        }
    },
    find_programver = {
        ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe"] = "19.44.35213",
        ["C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc"] = "12.9"
    },
    find_program_msvc_arch_x64_plat_windows_checktoolcc = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    find_program_msvc_arch_x64_plat_windows_checktoolsh = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    find_program_cuda_arch_x64_plat_windows_checktoolfcld = {
        nvfortran = false
    },
    find_program_gfortran_arch_x64_plat_windows_checktoolfcld = {
        gfortran = false,
        g95 = false
    }
}