{
    files = {
        [[build\.objs\infinirt\windows\x64\release\src\infinirt\infinirt.cc.obj]],
        [[build\windows\x64\release\infini-utils.lib]],
        [[build\windows\x64\release\infinirt-nvidia.lib]],
        [[build\windows\x64\release\infinirt-cpu.lib]]
    },
    values = {
        [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]],
        {
            "-nologo",
            "-machine:x64",
            [[-libpath:C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64]],
            [[-libpath:build\windows\x64\release]],
            "/opt:ref",
            "/opt:icf",
            "infinirt-cpu.lib",
            "infinirt-nvidia.lib",
            "cudart.lib",
            "infini-utils.lib",
            "cudadevrt.lib"
        }
    }
}