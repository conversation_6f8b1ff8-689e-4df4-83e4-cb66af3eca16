# InfiniCore算子实现完整指南

## 1. 算子开发流程

### 1.1 开发步骤概览
1. **设计算子接口** → 在文档中添加算子规范
2. **创建头文件** → 定义C语言API接口
3. **实现算子逻辑** → 创建operator.cc和平台实现
4. **编写测试** → Python单测和GGUF测试框架
5. **提交代码** → 遵循Git工作流规范

### 1.2 文件创建清单
```
include/infiniop/ops/[op_name].h          # API接口定义
src/infiniop/ops/[op_name]/
├── operator.cc                          # 通用接口实现
├── cpu/[op_name]_cpu.h                 # CPU实现头文件
├── cpu/[op_name]_cpu.cc                # CPU实现
├── nvidia/[op_name]_nvidia.cuh         # NVIDIA实现头文件
├── nvidia/[op_name]_nvidia.cu          # NVIDIA实现
└── cuda/kernel.cuh                     # CUDA kernel定义
test/infiniop/[op_name].py              # Python单元测试
test/infiniop-test/test_generate/testcases/[op_name].py  # GGUF测试
```

## 2. 算子接口设计

### 2.1 头文件模板 (`include/infiniop/ops/[op_name].h`)
```c
#ifndef __INFINIOP_[OP_NAME]_API_H__
#define __INFINIOP_[OP_NAME]_API_H__

#include "../operator_descriptor.h"

typedef struct InfiniopDescriptor *infiniop[OpName]Descriptor_t;

__C __export infiniStatus_t infiniopCreate[OpName]Descriptor(
    infiniopHandle_t handle,
    infiniop[OpName]Descriptor_t *desc_ptr,
    infiniopTensorDescriptor_t output_desc,
    infiniopTensorDescriptor_t input_desc_1,
    infiniopTensorDescriptor_t input_desc_2
);

__C __export infiniStatus_t infiniopGet[OpName]WorkspaceSize(
    infiniop[OpName]Descriptor_t desc, 
    size_t *size
);

__C __export infiniStatus_t infiniop[OpName](
    infiniop[OpName]Descriptor_t desc,
    void *workspace,
    size_t workspace_size,
    void *output,
    const void *input_1,
    const void *input_2,
    void *stream
);

__C __export infiniStatus_t infiniopDestroy[OpName]Descriptor(
    infiniop[OpName]Descriptor_t desc
);

#endif
```

### 2.2 更新主头文件
在 `include/infiniop.h` 中添加：
```c
#include "infiniop/ops/[op_name].h"
```

## 3. 算子实现

### 3.1 通用接口实现 (`src/infiniop/ops/[op_name]/operator.cc`)
```cpp
#include "../../operator.h"
#include "../../handle.h"
#include "infiniop/ops/[op_name].h"

#ifdef ENABLE_CPU_API
#include "cpu/[op_name]_cpu.h"
#endif
#if defined(ENABLE_NVIDIA_API) || defined(ENABLE_ILUVATAR_API)
#include "nvidia/[op_name]_nvidia.cuh"
#endif

__C infiniStatus_t infiniopCreate[OpName]Descriptor(
    infiniopHandle_t handle,
    infiniop[OpName]Descriptor_t *desc_ptr,
    infiniopTensorDescriptor_t output_desc,
    infiniopTensorDescriptor_t input_desc_1,
    infiniopTensorDescriptor_t input_desc_2) {

#define CREATE(CASE, NAMESPACE)                                            \
    case CASE:                                                             \
        return op::[op_name]::NAMESPACE::Descriptor::create(               \
            handle,                                                        \
            reinterpret_cast<op::[op_name]::NAMESPACE::Descriptor **>(desc_ptr), \
            output_desc,                                                   \
            {input_desc_1, input_desc_2})

    switch (handle->device) {
#ifdef ENABLE_CPU_API
        CREATE(INFINI_DEVICE_CPU, cpu);
#endif
#ifdef ENABLE_NVIDIA_API
        CREATE(INFINI_DEVICE_NVIDIA, nvidia);
#endif
    default:
        return INFINI_STATUS_DEVICE_TYPE_NOT_SUPPORTED;
    }
#undef CREATE
}

// 类似地实现其他函数...
```

### 3.2 Elementwise算子实现

#### CPU实现 (`src/infiniop/ops/[op_name]/cpu/[op_name]_cpu.h`)
```cpp
#ifndef __[OP_NAME]_CPU_H__
#define __[OP_NAME]_CPU_H__

#include "../../../elementwise/cpu/elementwise_cpu.h"

ELEMENTWISE_DESCRIPTOR([op_name], cpu)

namespace op::[op_name]::cpu {
typedef struct [OpName]Op {
public:
    static constexpr size_t num_inputs = 2;
    template <typename T>
    T operator()(const T &a, const T &b) const {
        // 实现具体的计算逻辑
        return a + b;  // 示例
    }
} [OpName]Op;
}

#endif
```

#### CPU实现源文件 (`src/infiniop/ops/[op_name]/cpu/[op_name]_cpu.cc`)
```cpp
#include "[op_name]_cpu.h"

namespace op::[op_name]::cpu {

Descriptor::~Descriptor() = default;

infiniStatus_t Descriptor::create(
    infiniopHandle_t handle_,
    Descriptor **desc_ptr,
    infiniopTensorDescriptor_t out_desc,
    std::vector<infiniopTensorDescriptor_t> input_desc_vec) {

    auto handle = reinterpret_cast<device::cpu::Handle *>(handle_);
    auto dtype = out_desc->dtype();

    // 数据类型检查
    CHECK_DTYPE(dtype, INFINI_DTYPE_F16, INFINI_DTYPE_F32, INFINI_DTYPE_F64, INFINI_DTYPE_BF16);

    // 形状检查
    const auto &a_desc = input_desc_vec.at(0);
    const auto &b_desc = input_desc_vec.at(1);
    const auto &c_shape = out_desc->shape();
    const auto &a_shape = a_desc->shape();
    const auto &b_shape = b_desc->shape();
    CHECK_SAME_SHAPE(c_shape, a_shape, b_shape);

    // 创建CPU elementwise描述符
    CREATE_ELEMENTWISE_CPU_DESCRIPTOR(handle, dtype, out_desc, input_desc_vec);

    return INFINI_STATUS_SUCCESS;
}

infiniStatus_t Descriptor::calculate(
    void *workspace,
    size_t workspace_size,
    void *output,
    std::vector<const void *> inputs,
    void *stream) const {

    switch (_dtype) {
    case INFINI_DTYPE_F16:
        return _device_info->calculate<[OpName]Op, fp16_t>(_info, output, inputs, stream);
    case INFINI_DTYPE_F32:
        return _device_info->calculate<[OpName]Op, float>(_info, output, inputs, stream);
    case INFINI_DTYPE_F64:
        return _device_info->calculate<[OpName]Op, double>(_info, output, inputs, stream);
    case INFINI_DTYPE_BF16:
        return _device_info->calculate<[OpName]Op, bf16_t>(_info, output, inputs, stream);
    default:
        return INFINI_STATUS_BAD_TENSOR_DTYPE;
    }
}
}
```

#### CUDA实现 (`src/infiniop/ops/[op_name]/cuda/kernel.cuh`)
```cpp
#ifndef __[OP_NAME]_CUDA_H__
#define __[OP_NAME]_CUDA_H__

namespace op::[op_name]::cuda {
typedef struct [OpName]Op {
public:
    static constexpr size_t num_inputs = 2;
    template <typename T>
    __device__ __forceinline__ T operator()(const T &a, const T &b) const {
        // CUDA特定的实现
        if constexpr (std::is_same_v<T, half2>) {
            return __hadd2(a, b);  // 示例
        } else if constexpr (std::is_same_v<T, half>) {
            return __hadd(a, b);   // 示例
        } else {
            return a + b;          // 示例
        }
    }
} [OpName]Op;
}

#endif
```

## 4. 测试实现

### 4.1 Python单元测试模板 (`test/infiniop/[op_name].py`)
```python
import torch
import ctypes
from ctypes import c_uint64
from libinfiniop import (
    LIBINFINIOP, TestTensor, get_test_devices, check_error,
    test_operator, get_args, debug, get_tolerance,
    profile_operation, TestWorkspace, InfiniDtype,
    InfiniDtypeNames, InfiniDeviceNames,
    infiniopOperatorDescriptor_t,
)

# 测试用例配置
_TEST_CASES_ = [
    # (shape, input1_stride, input2_stride, output_stride)
    ((13, 4), None, None, None),
    ((13, 4), (10, 1), (10, 1), (10, 1)),
    # 添加更多测试用例...
]

_TENSOR_DTYPES = [InfiniDtype.F16, InfiniDtype.F32, InfiniDtype.BF16]

_TOLERANCE_MAP = {
    InfiniDtype.F16: {"atol": 1e-3, "rtol": 1e-3},
    InfiniDtype.F32: {"atol": 1e-7, "rtol": 1e-7},
    InfiniDtype.BF16: {"atol": 1e-3, "rtol": 1e-3},
}

def [op_name](output, input1, input2):
    """PyTorch参考实现"""
    torch.add(input1, input2, out=output)  # 示例

def test(handle, device, shape, input1_stride=None, input2_stride=None, 
         output_stride=None, dtype=torch.float16, sync=None):
    # 创建测试张量
    input1 = TestTensor(shape, input1_stride, dtype, device)
    input2 = TestTensor(shape, input2_stride, dtype, device)
    output = TestTensor(shape, output_stride, dtype, device, mode="ones")

    # PyTorch参考计算
    [op_name](output.torch_tensor(), input1.torch_tensor(), input2.torch_tensor())

    # 创建算子描述符
    descriptor = infiniopOperatorDescriptor_t()
    check_error(LIBINFINIOP.infiniopCreate[OpName]Descriptor(
        handle, ctypes.byref(descriptor),
        output.descriptor, input1.descriptor, input2.descriptor))

    # 获取工作空间
    workspace_size = c_uint64(0)
    check_error(LIBINFINIOP.infiniopGet[OpName]WorkspaceSize(
        descriptor, ctypes.byref(workspace_size)))
    workspace = TestWorkspace(workspace_size.value, output.device)

    # 执行算子
    check_error(LIBINFINIOP.infiniop[OpName](
        descriptor, workspace.data(), workspace.size(),
        output.data(), input1.data(), input2.data(), None))

    # 验证结果
    atol, rtol = get_tolerance(_TOLERANCE_MAP, dtype)
    assert torch.allclose(output.actual_tensor(), output.torch_tensor(), 
                         atol=atol, rtol=rtol)

    check_error(LIBINFINIOP.infiniopDestroy[OpName]Descriptor(descriptor))

if __name__ == "__main__":
    args = get_args()
    for device in get_test_devices(args):
        test_operator(device, test, _TEST_CASES_, _TENSOR_DTYPES)
    print("\033[92mTest passed!\033[0m")
```

## 5. 编译和测试

### 5.1 编译算子
```bash
# 配置编译选项
xmake f --cpu=y --nv-gpu=y -cv

# 编译和安装
xmake build && xmake install
```

### 5.2 运行测试
```bash
# Python单元测试
python test/infiniop/[op_name].py --cpu

# 一键测试所有算子
python scripts/python_test.py --cpu
```

## 6. 最佳实践

### 6.1 性能优化建议
1. **复用现有框架**：优先使用elementwise、reduce等通用框架
2. **内存对齐**：注意CUDA内存对齐要求
3. **数据类型支持**：支持FP16、FP32、BF16等主流类型
4. **广播支持**：正确处理张量广播语义

### 6.2 代码质量
1. **错误处理**：使用CHECK_*宏进行参数验证
2. **内存管理**：正确管理描述符生命周期
3. **平台兼容**：确保在所有支持平台上正确编译
4. **测试覆盖**：包含边界情况和异常输入测试

### 6.3 文档要求
1. **API文档**：在InfiniCore-Documentation中添加算子规范
2. **代码注释**：关键算法和优化技巧需要注释
3. **测试说明**：测试用例的设计思路和覆盖范围

## 7. 实际开发示例：实现Sin算子

### 7.1 创建算子接口
首先在 `include/infiniop/ops/sin.h` 中定义接口：
```c
#ifndef __INFINIOP_SIN_API_H__
#define __INFINIOP_SIN_API_H__

#include "../operator_descriptor.h"

typedef struct InfiniopDescriptor *infiniopSinDescriptor_t;

__C __export infiniStatus_t infiniopCreateSinDescriptor(
    infiniopHandle_t handle,
    infiniopSinDescriptor_t *desc_ptr,
    infiniopTensorDescriptor_t output_desc,
    infiniopTensorDescriptor_t input_desc
);

__C __export infiniStatus_t infiniopGetSinWorkspaceSize(
    infiniopSinDescriptor_t desc, size_t *size);

__C __export infiniStatus_t infiniopSin(
    infiniopSinDescriptor_t desc,
    void *workspace, size_t workspace_size,
    void *output, const void *input, void *stream);

__C __export infiniStatus_t infiniopDestroySinDescriptor(
    infiniopSinDescriptor_t desc);

#endif
```

### 7.2 实现CPU版本
在 `src/infiniop/ops/sin/cpu/sin_cpu.h` 中：
```cpp
#ifndef __SIN_CPU_H__
#define __SIN_CPU_H__

#include "../../../elementwise/cpu/elementwise_cpu.h"
#include <cmath>

ELEMENTWISE_DESCRIPTOR(sin, cpu)

namespace op::sin::cpu {
typedef struct SinOp {
public:
    static constexpr size_t num_inputs = 1;
    template <typename T>
    T operator()(const T &x) const {
        return std::sin(x);
    }
} SinOp;
}

#endif
```

### 7.3 实现CUDA版本
在 `src/infiniop/ops/sin/cuda/kernel.cuh` 中：
```cpp
#ifndef __SIN_CUDA_H__
#define __SIN_CUDA_H__

namespace op::sin::cuda {
typedef struct SinOp {
public:
    static constexpr size_t num_inputs = 1;
    template <typename T>
    __device__ __forceinline__ T operator()(const T &x) const {
        if constexpr (std::is_same_v<T, half>) {
            return hsin(x);
        } else if constexpr (std::is_same_v<T, float>) {
            return sinf(x);
        } else {
            return sin(x);
        }
    }
} SinOp;
}

#endif
```

### 7.4 编写测试
在 `test/infiniop/sin.py` 中实现测试逻辑，参考add.py的模式。

这个指南提供了在InfiniCore框架中实现算子的完整流程，遵循这些步骤可以高效地开发出高质量的算子实现。
